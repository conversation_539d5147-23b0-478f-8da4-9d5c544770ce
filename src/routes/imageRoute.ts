import { Hono } from "hono";
import z from "zod/v4";
import { format } from "date-fns";
import appConfig from "../config";
import { AdjustImageService } from "../services/adjustImageService";
import { LocalFileService } from "../services/localFileService";
import logger from "../services/loggerService";
import prettyBytes from "pretty-bytes";
import { flatten } from "es-toolkit";
import { createFriendlyFileName, createRenamedFile } from "../utils/file";

const adjustImage = new Hono();

adjustImage.post("/", async (c) => {
	const requestId = c.get("requestId");

	const body = await c.req.parseBody({ all: true });

	const validated = z
		.object({
			files: z
				.file()
				.min(1)
				.max(appConfig.adjustImage.maxFileSize)
				.mime([
					"image/png",
					"image/jpeg",
					"image/gif",
					"image/webp",
					"image/svg+xml",
				])
				.array()
				.min(1)
				.max(appConfig.adjustImage.maxFiles),

			quality: z.coerce
				.number()
				.min(1)
				.max(100)
				.default(appConfig.adjustImage.defaultQuality),

			targetSize: z.coerce.number().min(1),
		})
		.parse({
			files: body.files,
			quality: c.req.query("quality"),
			targetSize: c.req.query("targetsize"),
		});

	logger.info(`Compress ${flatten([body.files]).length} Files`);

	const today = new Date();
	const compressDirPath = `compress_images/${format(today, "yyyy-MM-dd")}/${requestId}`;

	const localFileService = new LocalFileService(compressDirPath);
	const adjustImageService = new AdjustImageService(localFileService);

	const { quality, targetSize } = validated;

	const files = validated.files.map((file) => {
		const name = createFriendlyFileName(file.name);

		return createRenamedFile(file, name);
	});

	const compressResults: {
		size: string;
		originalSize: string;
		percentage: number;
		path: string;
	}[] = [];

	await Promise.all(
		files.map((file) =>
			adjustImageService.compress(file, quality, targetSize).then((bytes) => {
				compressResults.push({
					size: prettyBytes(bytes),
					originalSize: prettyBytes(file.size),
					percentage: Math.round((bytes / file.size) * 100),
					path: `/${localFileService.getFilePath(file.name)}`,
				});
			}),
		),
	);

	return c.json(compressResults);
});

export default adjustImage;
