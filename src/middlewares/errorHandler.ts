// src/middlewares/error-handler.ts
import type { Context } from "hono";
import { ZodError } from "zod/v4";
import config from "../config";
import logger from "../services/loggerService";
import { ApiError, ValidationError } from "../utils/errors";
import type { ContentfulStatusCode } from "hono/utils/http-status";

export const errorHandler = async (err: Error, c: Context) => {
	let statusCode: ContentfulStatusCode = 500;
	let message = "Internal Server Error";
	let code = "INTERNAL_SERVER_ERROR";
	let details: unknown = undefined;

	if (config.env === "development" || config.env === "staging") {
		logger.error(
			{ err, url: c.req.url, stack: err.stack },
			"An unhandled error occurred",
		);
	} else {
		logger.error(
			{ err: err.message, url: c.req.url },
			"An unhandled error occurred",
		);
	}

	if (err instanceof ZodError) {
		const validationError = new ValidationError(err);

		statusCode = validationError.statusCode;
		message = validationError.message;
		details = validationError.details;
		code = validationError.code || code;
	} else if (err instanceof ApiError) {
		statusCode = err.statusCode;
		message = err.message;
		code = err.code || code;
		details = err.details;
	} else if (err instanceof Error) {
		message = err.message;
	}

	const errorResponse = {
		statusCode: statusCode,
		message: message,
		code: code,
		timestamp: new Date().toISOString(),
		path: c.req.url,
		details,
		...(config.env === "development" && { stack: err.stack }),
	};

	return c.json(errorResponse, statusCode);
};
