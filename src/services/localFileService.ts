import fs from "node:fs";
import type { Readable, Writable } from "node:stream";
import appConfig from "../config";
import type { FileService } from "./fileService";

export class LocalFileService implements FileService {
	folderPath: string;

	constructor(folderPath: string) {
		this.folderPath = `${appConfig.staticPath}/${folderPath}`;

		if (!fs.existsSync(this.folderPath)) {
			fs.mkdirSync(this.folderPath, { recursive: true });
		}
	}

	async getSize(fileName: string): Promise<number> {
		const filePath = `${this.folderPath}/${fileName}`;

		try {
			const stats = await fs.promises.stat(filePath);
			return stats.size;
		} catch (error) {
			throw new Error(`Failed to get file size, path ${filePath}`);
		}
	}

	async write(fileName: string, data: Buffer) {
		const filePath = `${this.folderPath}/${fileName}`;

		try {
			await fs.promises.writeFile(filePath, data);
		} catch (error) {
			throw new Error(`Failed to write file, path ${filePath}`);
		}
	}

	createWriteStream(fileName: string): Writable {
		const filePath = `${this.folderPath}/${fileName}`;

		return fs.createWriteStream(filePath);
	}

	createReadStream(fileName: string): Readable {
		const filePath = `${this.folderPath}/${fileName}`;

		return fs.createReadStream(filePath);
	}

	getFilePath(fileName: string) {
		return `${this.folderPath}/${fileName}`;
	}
}
