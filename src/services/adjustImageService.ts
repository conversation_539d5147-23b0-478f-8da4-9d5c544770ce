import type { FileService } from "./fileService";
import { compressImageToTargetSize, getFileExtension } from "../utils/file";
import logger from "./loggerService";
import type { Logger } from "pino";

export class AdjustImageService {
	private fileService: FileService;
	private logger: Logger;

	constructor(fileService: FileService) {
		this.fileService = fileService;
		this.logger = logger;
	}

	async compress(
		file: File,
		quality: number,
		targetSize: number,
	): Promise<number> {
		const fileName = file.name;
		const fileWriteStream = this.fileService.createWriteStream(fileName);

		try {
			const fileExt = getFileExtension(fileName);

			if (!fileExt) {
				throw new Error(`Can not parse file extension, file name: ${fileName}`);
			}

			const imageBuffer = await file.arrayBuffer();

			const compressSize = await compressImageToTargetSize({
				buffer: imageBuffer,
				outputStream: fileWriteStream,
				targetSizeInBytes: targetSize,
				imageOptions: {
					quality,
					targetFormat: fileExt,
				},
				logger: this.logger.child({
					fileName,
				}),
			});

			return compressSize;
		} catch (error) {
			throw new Error("Compress File Fail!");
		} finally {
			fileWriteStream?.destroy();
		}
	}
}
