// src/utils/errors.ts
import type { ContentfulStatusCode } from "hono/utils/http-status";
import type { ZodError } from "zod/v4";

// Base custom error class
export class ApiError<T = unknown> extends Error {
	public statusCode: ContentfulStatusCode;
	public code?: string;
	public details?: T; // Có thể là mảng các lỗi validation, hoặc object tùy chỉnh

	constructor(
		message: string,
		statusCode: ContentfulStatusCode,
		code?: string,
		details?: T,
	) {
		super(message);
		this.name = this.constructor.name; // Đặt tên lỗi là tên class
		this.statusCode = statusCode;
		this.code = code;
		this.details = details;

		// Giữ lại stack trace
		if (Error.captureStackTrace) {
			Error.captureStackTrace(this, this.constructor);
		}
	}
}

// Specific error types
export class BadRequestError<T> extends ApiError<T> {
	constructor(message = "Bad Request", details?: T) {
		super(message, 400, "BAD_REQUEST", details);
	}
}

export class UnauthorizedError<T> extends ApiError<T> {
	constructor(message = "Unauthorized", details?: T) {
		super(message, 401, "UNAUTHORIZED", details);
	}
}

export class ForbiddenError<T> extends ApiError<T> {
	constructor(message = "Forbidden", details?: T) {
		super(message, 403, "FORBIDDEN", details);
	}
}

export class NotFoundError<T> extends ApiError<T> {
	constructor(message = "Not Found", details?: T) {
		super(message, 404, "NOT_FOUND", details);
	}
}

export class ConflictError<T> extends ApiError<T> {
	constructor(message = "Conflict", details?: T) {
		super(message, 409, "CONFLICT", details);
	}
}

export class InternalServerError<T> extends ApiError<T> {
	constructor(message = "Internal Server Error", details?: T) {
		super(message, 500, "INTERNAL_SERVER_ERROR", details);
	}
}

export class ValidationError extends BadRequestError<
	{ field: string; message: string; code: string }[]
> {
	constructor(zodError: ZodError) {
		const details = zodError.issues.map((err) => ({
			field: err.path.join("."),
			message: err.message,
			code: err.code,
		}));

		super("Validation Failed", details);
		this.code = "VALIDATION_ERROR";
	}
}
