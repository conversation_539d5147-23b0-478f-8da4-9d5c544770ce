import fs from "node:fs";
import stream from "node:stream";
import type { ReadableStream } from "node:stream/web";
import type { SetRequired } from "type-fest";
import sharp from "sharp";
import appConfig from "../config";
import type { SizeUnit } from "../types/common";
import { ByteCounterTransform } from "./stream";
import prettyBytes from "pretty-bytes";
import type { Logger } from "pino";

export function createRenamedFile(
	originalFile: File,
	newFileName: string,
): File {
	if (!(originalFile instanceof File)) {
		throw new Error("Input must be a valid File object.");
	}

	const renamedFile = new File([originalFile], newFileName, {
		type: originalFile.type,
		lastModified: originalFile.lastModified,
	});

	return renamedFile;
}

export function createFriendlyFileName(inputString: string): string {
	let fileName = inputString.toLowerCase().trim();

	fileName = fileName.replace(/[^a-z0-9-_\.]+/g, "-");

	fileName = fileName.replace(/--+/g, "-");

	fileName = fileName.replace(/^-+|-+$/g, "");

	if (fileName === "") {
		return "untitled";
	}

	return fileName;
}

export function convertToBytes(size: number, unit: SizeUnit): number {
	const multiplier = unit === "kb" ? 1024 : 1024 * 1024;
	return size * multiplier;
}

export function getFileExtension(filename: string) {
	const parts = filename.split(".");
	if (parts.length > 1) {
		return parts.pop();
	}
	return "";
}

export function getFileSize(filePath: string): number {
	const stats = fs.statSync(filePath);
	return stats.size;
}

/**
 * Creates a Sharp pipeline for image transformation based on the provided options.
 * @param logger - The pino logger instance.
 * @param sharpInstance - The initial sharp instance.
 * @param options - The transformation options (quality, format, dimensions).
 * @returns A sharp instance configured with the specified transformations.
 */
export const createImageTransformPipeline = (
	logger: Logger,
	sharpInstance: sharp.Sharp,
	options: Partial<{
		quality: number;
		targetFormat: string;
		width: number;
		height: number;
	}>,
): sharp.Sharp => {
	let pipeline = sharpInstance.clone();

	const { quality, width, height, targetFormat } = options;

	if (width || height) {
		pipeline = pipeline.resize(width, height);
	}

	if (quality) {
		// PNG quality is 0-9, while others are 0-100. We scale it.
		// https://sharp.pixelplumbing.com/api-output#png
		const pngQuality = Math.min(Math.max(Math.round(quality / 10), 0), 9);

		switch (targetFormat) {
			case "jpeg":
			case "jpg":
				pipeline = pipeline.jpeg({ quality });
				break;
			case "png":
				pipeline = pipeline.png({ quality: pngQuality });
				break;
			case "webp":
				pipeline = pipeline.webp({ quality });
				break;
			case "avif":
				pipeline = pipeline.avif({ quality });
				break;
			case "heif":
				pipeline = pipeline.heif({ quality });
				break;
			case "tiff":
				pipeline = pipeline.tiff({ quality });
				break;
			default:
				logger.warn(
					`[createImageTransformPipeline] Unsupported format: ${targetFormat}. Skipping quality adjustment.`,
				);
				break;
		}
	}

	return pipeline;
};

export function blobToNodeReadableStream(blob: Blob): stream.Readable {
	const webReadableStream = blob.stream();

	return stream.Readable.fromWeb(
		webReadableStream as ReadableStream<Uint8Array>,
	);
}

/**
 * Compresses an image with a specific quality and measures the resulting size.
 * @param args - The buffer, image options and pino logger instance.
 * @returns A promise that resolves with the compressed stream and its size.
 */
export const compressImage = async (args: {
	logger: Logger;
	buffer: ArrayBuffer;
	imageOptions: {
		quality: number;
		targetFormat: string;
		width?: number;
		height?: number;
	};
}): Promise<{ size: number; compressedStream: stream.Readable }> => {
	const { logger, imageOptions, buffer } = args;

	try {
		const sharpInstance = sharp(buffer);

		const transformPipeline = createImageTransformPipeline(
			logger,
			sharpInstance,
			imageOptions,
		);

		const byteCounter = new ByteCounterTransform();
		const compressedStream = new stream.PassThrough();

		transformPipeline.pipe(byteCounter).pipe(compressedStream);

		await new Promise<void>((resolve, reject) => {
			transformPipeline.on("error", reject);
			byteCounter.on("error", reject);
			byteCounter.on("finish", resolve);
		});

		return {
			size: byteCounter.totalBytes,
			compressedStream,
		};
	} catch (error) {
		logger.error(
			`Error during image compression with quality ${imageOptions.quality}`,
			error,
		);
		throw error;
	}
};

/**
 * Compresses an image to a target size by finding the optimal quality using binary search.
 * It writes the best-effort result to the output stream.
 * @param args - Contains the image buffer, output stream, target size, pino logger instance and image options.
 * @returns The final size of the compressed image in bytes.
 */
export const compressImageToTargetSize = async (args: {
	logger: Logger;
	buffer: ArrayBuffer;
	outputStream: stream.Writable;
	targetSizeInBytes: number;
	imageOptions: {
		width?: number;
		height?: number;
		quality?: number;
		targetFormat: string;
	};
}): Promise<number> => {
	const {
		logger,
		buffer,
		outputStream,
		targetSizeInBytes,
		imageOptions: baseImageOptions,
	} = args;

	const initialQuality =
		baseImageOptions.quality || appConfig.adjustImage.defaultQuality;

	const imageOptions: SetRequired<typeof baseImageOptions, "quality"> = {
		...baseImageOptions,
		quality: initialQuality,
	};

	const pipeToOutputStream = (
		readableStream: stream.Readable,
	): Promise<void> => {
		return new Promise((resolve, reject) => {
			readableStream.pipe(outputStream);
			readableStream.on("error", reject);
			outputStream.on("error", reject);
			outputStream.on("finish", resolve);
		});
	};

	// First, try with the initial/default quality
	const { size: initialSize, compressedStream: initialStream } =
		await compressImage({
			logger,
			buffer,
			imageOptions,
		});

	if (initialSize <= targetSizeInBytes) {
		logger.info(
			`Initial quality ${initialQuality} is sufficient. Size: ${prettyBytes(initialSize)}.`,
		);
		await pipeToOutputStream(initialStream);
		return initialSize;
	}

	// Check the minimum possible size at quality=1
	const { size: minSize, compressedStream: minSizeStream } =
		await compressImage({
			logger,
			buffer,
			imageOptions: { ...imageOptions, quality: 1 },
		});

	if (minSize > targetSizeInBytes) {
		logger.warn(
			`Cannot meet target size ${prettyBytes(targetSizeInBytes)}. Using smallest possible size: ${prettyBytes(minSize)}.`,
		);
		await pipeToOutputStream(minSizeStream);
		return minSize;
	}

	// Binary search for the best quality
	logger.info(
		`Starting binary search for best quality to meet target size ${prettyBytes(targetSizeInBytes)}.`,
	);

	let lowQuality = 1;
	let highQuality = initialQuality;
	let bestQuality = lowQuality;
	let bestSize = minSize;
	let bestStream: stream.Readable = minSizeStream;

	const MAX_ITERATIONS = 8;

	for (let i = 0; i < MAX_ITERATIONS; i++) {
		const currentQuality = Math.round((lowQuality + highQuality) / 2);

		// If we're stuck, break the loop
		if (currentQuality === lowQuality || currentQuality === highQuality) {
			break;
		}

		const { size: currentSize, compressedStream: currentStream } =
			await compressImage({
				logger,
				buffer,
				imageOptions: { ...imageOptions, quality: currentQuality },
			});

		logger.info(
			`  Iteration ${i + 1}: Quality ${currentQuality} -> Size ${prettyBytes(currentSize)}`,
		);

		if (currentSize <= targetSizeInBytes) {
			// This is a potential candidate, store it
			bestQuality = currentQuality;
			bestSize = currentSize;
			bestStream = currentStream;

			// Try for better quality (and larger size)
			lowQuality = currentQuality;
		} else {
			// Too large, reduce quality
			highQuality = currentQuality;
		}
	}

	logger.info(
		`Binary search finished. Best quality: ${bestQuality}, final size: ${prettyBytes(bestSize)}. Finalizing...`,
	);

	await pipeToOutputStream(bestStream);
	return bestSize;
};
