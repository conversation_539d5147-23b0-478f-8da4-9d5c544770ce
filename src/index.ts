import { Hono } from "hono";
import { cors } from "hono/cors";
import { serveStatic } from "@hono/node-server/serve-static";
import { logger as honoLogger } from "hono/logger";
import { serve } from "@hono/node-server";
import { requestId, type RequestIdVariables } from "hono/request-id";
import compress from "./routes/imageRoute";
import { errorHandler } from "./middlewares/errorHandler";

const app = new Hono<{
	Variables: RequestIdVariables;
}>();

app.use("/*", cors());

app.use(honoLogger());

app.use("*", requestId());

app.use("/static/*", serveStatic({ root: "./" }));

const api = new Hono().basePath("/api");

api.route("/compress", compress);

api.get("/health", (c) => {
	return c.json({ status: "ok" });
});

app.route("/", api);

app.onError(errorHandler);

serve({
	fetch: app.fetch,
	port: 8000,
});
