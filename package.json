{"name": "image-compression-nodejs", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js"}, "dependencies": {"@hono/node-server": "^1.14.1", "@hono/zod-validator": "^0.7.0", "@libsql/client": "^0.15.9", "@paralleldrive/cuid2": "^2.2.2", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "es-toolkit": "^1.39.4", "file-type": "^21.0.0", "hono": "^4.7.8", "nanoid": "^5.1.5", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "pretty-bytes": "^7.0.0", "sharp": "^0.34.1", "type-fest": "^4.41.0", "zod": "^3.25.67"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/node": "^20.11.17", "drizzle-kit": "^0.31.1", "tsx": "^4.7.1", "typescript": "^5.8.3"}}